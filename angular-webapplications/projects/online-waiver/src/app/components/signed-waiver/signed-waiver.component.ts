import { Component, input } from '@angular/core';
import { CustomerSignedWaiverDTO } from '../../models/customer-signed-waiver.model';
import { DatePipe } from '@angular/common';

@Component({
  selector: 'app-signed-waiver',
  imports: [DatePipe],
  templateUrl: './signed-waiver.component.html',
  styleUrl: './signed-waiver.component.scss'
})
export class SignedWaiverComponent {
  signedWaivers = input<CustomerSignedWaiverDTO[]>([]);

viewWaiver(code: string|null): void {
    console.log(`Viewing waiver: ${code}`);
    // Implement view functionality
}

downloadWaiver(code: string|null): void {
    console.log(`Downloading waiver: ${code}`);
    // Implement download functionality
}
}
