import { ComponentFixture, TestBed } from '@angular/core/testing';
import { SignedWaiverComponent } from './signed-waiver.component';
import { CustomerSignedWaiverDTO } from '../../models/customer-signed-waiver.model';
import { signal } from '@angular/core';

describe('SignedWaiverComponent', () => {
  let component: SignedWaiverComponent;
  let fixture: ComponentFixture<SignedWaiverComponent>;

  // Sample Base64 PDF content (minimal PDF header for testing)
  const validPdfBase64 = btoa('%PDF-1.4\n1 0 obj\n<<\n/Type /Catalog\n/Pages 2 0 R\n>>\nendobj\n2 0 obj\n<<\n/Type /Pages\n/Kids [3 0 R]\n/Count 1\n>>\nendobj\n3 0 obj\n<<\n/Type /Page\n/Parent 2 0 R\n/MediaBox [0 0 612 792]\n>>\nendobj\nxref\n0 4\n0000000000 65535 f \n0000000009 00000 n \n0000000074 00000 n \n0000000120 00000 n \ntrailer\n<<\n/Size 4\n/Root 1 0 R\n>>\nstartxref\n174\n%%EOF');
  const invalidBase64 = 'invalid-base64-content';

  const mockWaivers: CustomerSignedWaiverDTO[] = [
    {
      CustomerSignedWaiverId: 1,
      CustomerSignedWaiverHeaderId: 1,
      WaiverSetDetailId: 1,
      SignedWaiverFileName: 'test-waiver.pdf',
      WaiverName: 'Test Waiver',
      WaiverFileName: 'waiver.pdf',
      SignedFor: 226,
      SignedForName: 'John Doe',
      ExpiryDate: '2025-12-31',
      IsActive: true,
      DeactivatedBy: null,
      DeactivationDate: null,
      DeactivationApprovedBy: null,
      Guid: 'test-guid-123',
      SynchStatus: true,
      MasterEntityId: 1,
      CreatedBy: 'system',
      CreationDate: '2025-01-01',
      LastUpdatedBy: 'system',
      LastUpdateDate: '2025-01-01',
      SiteId: 1010,
      WaiverSignedImageList: [],
      CustomerContentForWaiverDTOList: [],
      SignedBy: 226,
      SignedByName: 'John Doe',
      SignedDate: '2025-01-01',
      WaiverCode: 'TEST001',
      WaiverSetId: 1,
      WaiverSetDescription: 'Test Waiver Set',
      SignedWaiverFileContentInBase64Format: validPdfBase64,
      GuardianId: null,
      IsChangedRecursive: false,
      IsChanged: false
    }
  ];

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [SignedWaiverComponent]
    }).compileComponents();

    fixture = TestBed.createComponent(SignedWaiverComponent);
    component = fixture.componentInstance;
    
    // Set up the input signal
    fixture.componentRef.setInput('signedWaivers', mockWaivers);
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should validate valid PDF Base64 content', () => {
    const isValid = component['isValidBase64'](validPdfBase64);
    expect(isValid).toBeTruthy();
  });

  it('should reject invalid Base64 content', () => {
    const isValid = component['isValidBase64'](invalidBase64);
    expect(isValid).toBeFalsy();
  });

  it('should handle viewWaiver with valid waiver code', () => {
    component.viewWaiver('TEST001');

    expect(component.showPdfModal()).toBeTruthy();
    expect(component.currentWaiverCode()).toBe('TEST001');
    expect(component.currentPdfUrl()).toBeTruthy();
  });

  it('should handle viewWaiver with invalid waiver code', () => {
    spyOn(console, 'error');
    
    component.viewWaiver('INVALID');

    expect(console.error).toHaveBeenCalledWith('Waiver not found or PDF content not available');
  });

  it('should handle downloadWaiver with valid waiver code', () => {
    const mockLink = {
      href: '',
      download: '',
      click: jasmine.createSpy('click')
    };
    
    spyOn(document, 'createElement').and.returnValue(mockLink as any);
    spyOn(document.body, 'appendChild');
    spyOn(document.body, 'removeChild');
    spyOn(URL, 'createObjectURL').and.returnValue('blob:test-url');
    spyOn(URL, 'revokeObjectURL');

    component.downloadWaiver('TEST001');

    expect(mockLink.download).toBe('waiver-TEST001.pdf');
    expect(mockLink.click).toHaveBeenCalled();
  });

  it('should handle downloadWaiver with invalid waiver code', () => {
    spyOn(console, 'error');
    
    component.downloadWaiver('INVALID');

    expect(console.error).toHaveBeenCalledWith('Waiver not found or PDF content not available');
  });

  it('should handle null waiver code', () => {
    spyOn(console, 'error');

    component.viewWaiver(null);
    component.downloadWaiver(null);

    expect(console.error).toHaveBeenCalledWith('Waiver code is required');
    expect(console.error).toHaveBeenCalledTimes(2);
  });

  it('should close PDF modal', () => {
    // First open the modal
    component.viewWaiver('TEST001');
    expect(component.showPdfModal()).toBeTruthy();

    // Then close it
    component.closePdfModal();
    expect(component.showPdfModal()).toBeFalsy();
    expect(component.currentPdfUrl()).toBeNull();
    expect(component.currentWaiverCode()).toBe('');
  });
});
