export interface CustomerSignedWaiverResponse {
    data: CustomerSignedWaiverDTO[];
}

export interface CustomerSignedWaiverDTO {
    CustomerSignedWaiverId: number | null;
    CustomerSignedWaiverHeaderId: number | null;
    WaiverSetDetailId: number | null;
    SignedWaiverFileName: string | null;
    WaiverName: string | null;
    WaiverFileName: string | null;
    SignedFor: number | null;
    SignedForName: string | null;
    ExpiryDate: string | null;
    IsActive: boolean | null;
    DeactivatedBy: string | null;
    DeactivationDate: string | null;
    DeactivationApprovedBy: string | null;
    Guid: string | null;
    SynchStatus: boolean | null;
    MasterEntityId: number | null;
    CreatedBy: string | null;
    CreationDate: string | null;
    LastUpdatedBy: string | null;
    LastUpdateDate: string | null;
    SiteId: number | null;
    WaiverSignedImageList: any[] | null;
    CustomerContentForWaiverDTOList: any[] | null;
    SignedBy: number | null;
    SignedByName: string | null;
    SignedDate: string | null;
    WaiverCode: string | null;
    WaiverSetId: number | null;
    WaiverSetDescription: string | null;
    SignedWaiverFileContentInBase64Format: string;
    GuardianId: number | null;
    IsChangedRecursive: boolean | null;
    IsChanged: boolean | null;
  }
  


 