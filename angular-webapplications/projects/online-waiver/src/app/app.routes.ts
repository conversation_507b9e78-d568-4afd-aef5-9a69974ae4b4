/**
 * @fileoverview
 *
 * <AUTHOR>
 * @version 1.0.0
 * @created 2024-09-18
 */

import { Routes } from '@angular/router';
import { LandingPageComponent } from './pages/landing/landing.page.component';
import { TestUiComponentsComponent } from './pages/test-ui-components/test-ui-components.component';
import { TestSsrComponent } from './pages/test-ssr/test-ssr.component';
import { PageNotFoundComponent } from 'lib-app-core';
import { SignWaiverComponent } from './components/sign-waiver/sign-waiver.component';
import { AddParticipantsComponent } from './components/add-participants/add-participants.component';
import { MySignedWaiversComponent } from './pages/my-account/my-signed-waivers/my-signed-waivers.component';
import { MyRelationsComponent } from './pages/my-account/my-relations/my-relations.component';
import { MainLayoutComponent } from './shared/main-layout/main-layout.component';
import { ProfileComponent } from 'lib-my-account';
import { SiteSelectionComponent } from './components/site-selection/site-selection.component';
import { AuthGuard } from 'lib/lib-auth/src/lib/guards/auth.guard';
import { GuestGuard } from 'lib/lib-auth/src/lib/guards/guest.guard';
import { WelcomeToWaiversComponent } from './components/welcome-to-waivers/welcome-to-waivers.component';
import { RegistrationFormWaiverComponent } from './components/registration-form-waiver/registration-form-waiver.component';
import { LoginFormComponent } from 'lib-auth';
import { AuthComponent } from './components/auth/auth.component';
import { PasswordResetComponent } from 'lib/lib-auth/src/lib/components/reset-password/reset-password.component';
import { StatusResultComponent } from 'lib/lib-auth/src/lib/components/status-result/status-result.component';
// import { TestPage2Component } from './pages/test-page-2/test-page-2.component';

export const routes: Routes = [

    { path: '', component: LandingPageComponent, canActivate: [GuestGuard]},
    { 
        path: 'auth', 
        component: AuthComponent,
        children: [
            { path: 'register', component: RegistrationFormWaiverComponent,canActivate: [GuestGuard] },
            { path: 'login', component: LoginFormComponent, canActivate: [GuestGuard] },
            { path: '', redirectTo: 'register', pathMatch: 'full'  }
        ]
    },
    {
        path: 'waivers/add-participants', component: AddParticipantsComponent,canActivate: [AuthGuard], data: { breadcrumb: 'Add Participants' }
    },
    {
        path: 'waivers/sign-waiver/:custId/:waiverSetId', component: SignWaiverComponent,canActivate: [AuthGuard], data: { breadcrumb: 'Sign Waiver' }
    },
    {
        path: 'my-accounts',
        component: MainLayoutComponent,
        canActivate: [AuthGuard],
        data: { breadcrumb: 'My Accounts' },
        children: [
            {
                path: 'my-profile',
                component: ProfileComponent,
                data: { breadcrumb: 'My Profile', canActivate: [AuthGuard] },
            },
            {
                path: '**',
                redirectTo: '/my-accounts/my-profile',
            },
        ],
    },
    {
        path: 'waivers',
        component: MainLayoutComponent,
        //canActivate: [AuthGuard],
        data: { breadCrumb: 'Waivers' },
        children: [
            {
                path: 'my-signed-waivers',
                component: MySignedWaiversComponent,
                data: { breadcrumb: 'My Signed Waivers'}
            },
            {
                path: 'my-relations',
                component: MyRelationsComponent,
                data: { breadcrumb: 'My Relations', canActivate: [AuthGuard] }
            },
            { path: '**', redirectTo: '/waivers/my-signed-waivers' },
        ],
    },
    {
        path: 'auth/password-reset',
        component: PasswordResetComponent,
    },
    { path: 'success', component: StatusResultComponent, data: { status: 'success' } },
    { path: 'failure', component: StatusResultComponent, data: { status: 'failure' } },
    { path: 'site-selection', component: SiteSelectionComponent },
    { path: 'ui-components', component: TestUiComponentsComponent },
    { path: 'test-ssr', component: TestSsrComponent },
    { path: '**', component: PageNotFoundComponent },
];
