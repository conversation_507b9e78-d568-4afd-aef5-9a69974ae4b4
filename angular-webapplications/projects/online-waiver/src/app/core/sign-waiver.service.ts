/**
 * @fileoverview
 *
 * <AUTHOR>
 * @version 1.0.0
 * @created 2025-04-07
 */

import { HttpClient, HttpHeaders } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import {
    DEFAULT_APP_CONFIG_TOKEN,
    EnvService,
    getConstantValue,
} from 'lib-app-core';
import { forkJoin, Observable } from 'rxjs';
import { PreviewWaiverService } from './preview-waiver.service';
import { DomSanitizer } from '@angular/platform-browser';
import { CustomerSignedWaiverResponse } from '../models/customer-signed-waiver.model';

export class CreateCustomerWaiverRequestDTO {
    'Channel': string;
    'SignForCustomersIdList': number[];
    'SignatoryGuestCustomerDTO': SignatoryGuestCustomerDTO;
    'SignForGuestCustomersDTOList': any[];
}

export interface SignatoryGuestCustomerDTO {}

@Injectable({
    providedIn: 'root',
})
export abstract class SignWaiverService {
    private envService = inject(EnvService);
    private defaultAppConfig = inject(DEFAULT_APP_CONFIG_TOKEN);

    protected parafaitApiBaseUrl: string = this.envService.parafaitApiBaseUrl;
    // protected siteId: number = this.defaultAppConfig['siteId']; // Default site identifier
    protected siteId: number = 1010; // Default site identifier
    private waiverAPIEndPoints: any; //api end points

    constructor(
        private http: HttpClient,
        private previewWaiverService: PreviewWaiverService,
        private _sanitizer: DomSanitizer
    ) {
        this.waiverAPIEndPoints = getConstantValue('API_ENDPOINTS');
    }

    /** Gets Customer Signed Waiver DTO list that has Signed for , Signed By and waiver details using primary customer ID  */
    getSignedCustomersBasedOnPrimaryCustId(custId: number) {
        return this.http.get(
            `${this.parafaitApiBaseUrl}/Customer/Customers?customerId=${custId}&buildChildRecords=true&loadAdultOnly=true&loadSignedWaivers=true&loadSignedWaiverFileContent=false`
        );
    }

    /**
     * Gets signed waivers for a specific customer
     * @param customerId - The customer ID to fetch signed waivers for
     * @returns Observable with signed waiver data
     */
    getSignedWaivers(customerId: number ): Observable<CustomerSignedWaiverResponse> {
        return this.http.get<CustomerSignedWaiverResponse>(
            `${this.parafaitApiBaseUrl}${this.waiverAPIEndPoints.GET_SIGNED_WAIVERS}?customerId=${customerId}`,
        );
    }

    getCustomerById(custId: number) {
        return this.http.get(
            `${this.parafaitApiBaseUrl}/Customer/Customers?customerId=${custId}&buildChildRecords=true&loadAdultOnly=true&loadSignedWaivers=false&loadSignedWaiverFileContent=false`
        );
    }

    //Loads the waiver file contents
    getSignedWaiverFileContentBasedOnPrimaryCustId(custId: number) {
        return this.http.get(
            `${this.parafaitApiBaseUrl}/Customer/Customers?customerId=${custId}&buildChildRecords=true&loadAdultOnly=true&loadSignedWaivers=true&loadSignedWaiverFileContent=true&activeRecordsOnly=true`
        );
    }

    getCommonLookup(
        moduleName: string = 'WAIVERS',
        entityName: string = 'SETRELATIONSHIPTYPE'
    ) {
        return this.http.get(
            `${this.parafaitApiBaseUrl}/CommonServices/CommonLookup?moduleName=${moduleName}&entityName=${entityName}`
        );
    }

    getRelatedCustomer(custId: number) {
        return this.http.get(
            `${this.parafaitApiBaseUrl}/Customer/CustomerRelationships?customerId=${custId}&buildChildRecords=true`
        );
    }

    //this end point exists in preview waiver component - with change in query params and request type
    createCustomerWaiver(
        loggedInCustId: number,
        selectedWaiverId: number,
        channel: string,
        selectedCustomersForSign: number[]
    ) {
        const createCustomerWaiverPayload =
            new CreateCustomerWaiverRequestDTO();
        createCustomerWaiverPayload.Channel = channel;
        createCustomerWaiverPayload.SignForCustomersIdList =
            selectedCustomersForSign;

        return this.http.post(
            `${this.parafaitApiBaseUrl}/Customer/${loggedInCustId}/Waiver/${selectedWaiverId}/CreateCustomerWaiver?previewMode=false&postMode=false`,
            createCustomerWaiverPayload
        );
    }

    async contructPrimaryAndRelatedCustomerDetails(
        custId: number,
        waiverSetId: number
    ) {
        return new Promise<any>((resolve, reject) => {
            try {
                const relatedCustomersReq = this.getRelatedCustomer(custId);
                const signedCustomersBasedOnPrimaryIdReq =
                    this.getSignedCustomersBasedOnPrimaryCustId(custId);
                return forkJoin({
                    relatedCustomerResp: relatedCustomersReq,
                    signedCustomersRespBasedOnPrimaryId:
                        signedCustomersBasedOnPrimaryIdReq,
                }).subscribe({
                    next: (resp: any) => {
                        //it will be always primary customer detail
                        let primaryCustomerDetails =
                            resp.signedCustomersRespBasedOnPrimaryId.data[0]
                                .ProfileDTO;

                        let participantsSignedWaivers =
                            resp.signedCustomersRespBasedOnPrimaryId.data[0].CustomerSignedWaiverDTOList.filter(
                                (p: any) =>
                                    p.SignedFor == primaryCustomerDetails.Id
                            );
                        let signedWaivers = participantsSignedWaivers.map(
                            (p: any) => p.WaiverSetId
                        );

                        let primaryCustomerDetail = {
                            Id: primaryCustomerDetails.Id,
                            FirstName: primaryCustomerDetails.FirstName,
                            LastName: primaryCustomerDetails.LastName,
                            EmailId: primaryCustomerDetails.Email,
                            DateOfBirth: primaryCustomerDetails.DateOfBirth,
                            IsAdult: false,
                            IsSelected: false,
                            IsCurrentlyAdded: false,
                            IsActive: true,
                            PhoneNumber: primaryCustomerDetails.PhoneNumber,
                            Signature: null,
                            Gender: primaryCustomerDetails.Gender,
                            SignedWaiverIds: signedWaivers,
                            SignedStatus: signedWaivers.includes(waiverSetId),
                        };

                        this.constructParticipantsDetailsForDisplay(
                            resp.relatedCustomerResp.CustomerRelationshipDTO,
                            resp.signedCustomersRespBasedOnPrimaryId.data[0]
                                .CustomerSignedWaiverDTOList,
                            waiverSetId
                        ).then((relatedCustomerDetails) => {
                            resolve({
                                primaryCustomerDetails: primaryCustomerDetail,
                                relatedCustomerDetails: relatedCustomerDetails,
                            });
                        });
                        /** Fetches the related customer DTO */
                        // const observables: Observable<any>[] = resp.relatedCustomerResp.CustomerRelationshipDTO.map((obj: any) =>
                        //     this.getCustomerById(obj.RelatedCustomerId)
                        // )

                        // forkJoin(observables).subscribe({
                        //     next: (results: any) => {
                        //         console.log(JSON.stringify(results));
                        //     },
                        //     error: (err) => {
                        //         console.log(err)
                        //     }
                        // })
                    },
                    error: (err) => {
                        console.error(
                            'Error in constructPrimaryAndRelatedCustomerDetails:',
                            err
                        );
                        reject(err);
                    },
                });
            } catch (error) {
                console.error(
                    'Error in constructPrimaryAndRelatedCustomerDetails:',
                    error
                );
                throw error;
            }
        });
    }

    constructParticipantsDetailsForDisplay(
        relatedCustomers: any,
        signedCustomerObj: any,
        waiverSetId: number
    ) {
        return new Promise<any>((resolve, reject) => {
            let relatedCustomerDetails: any = [];
            relatedCustomers.forEach((customerObj: any) => {
                let relatedCustomerSignedWaivers = signedCustomerObj.filter(
                    (p: any) => p.SignedFor == customerObj.RelatedCustomerId
                );
                let signedWaivers = relatedCustomerSignedWaivers.map(
                    (p: any) => p.WaiverSetId
                );

                let customerDetail = {
                    Id: customerObj.RelatedCustomerId,
                    FirstName: customerObj.RelatedCustomerName,
                    LastName: customerObj.LastName,
                    EmailId: customerObj.RelatedCustomerDTO.Email,
                    DateOfBirth: customerObj.RelatedCustomerDTO.DateOfBirth,
                    RelationShip: customerObj.CustomerRelationshipTypeId,
                    IsAdult: false,
                    IsSelected: false,
                    ChildName: null, // based on configuration willl be displayed
                    ChildAge: null, // based on configuration willl be displayed
                    IsCurrentlyAdded: false,
                    IsActive: true,
                    RelationshipId: customerObj.Id,
                    PhoneNumber: customerObj.RelatedCustomerDTO.PhoneNumber,
                    Signature: null,
                    Gender: customerObj.Gender,
                    SignedWaiverIds: signedWaivers,
                    SignedStatus: signedWaivers.includes(waiverSetId),
                };
                relatedCustomerDetails.push(customerDetail);
            });
            resolve(relatedCustomerDetails);
        });
    }

    /** Sign wiaver component related functionalities starts  */
    constructParticipantsAndPDFView(
        loggedCustId: number,
        selectedWaiverSetId: number,
        selectedCustIdsForSignature: number[]
    ) {
        return new Promise<any>((resolve, reject) => {
            try {
                // const relatedCustomersReq = this.getRelatedCustomer(loggedCustId);
                // const signedCustomersBasedOnPrimaryIdReq = this.getSignedCustomersBasedOnPrimaryCustId(loggedCustId);
                const relatedCustAndsignedCustomersBasedOnPrimaryIdReq =
                    this.contructPrimaryAndRelatedCustomerDetails(
                        loggedCustId,
                        selectedWaiverSetId
                    );
                const createCustomerWaiverReq = this.createCustomerWaiver(
                    loggedCustId,
                    selectedWaiverSetId,
                    'WEBSITE',
                    selectedCustIdsForSignature
                );

                return forkJoin({
                    relatedCustAndsignedCustomersBasedOnPrimaryIdResp:
                        relatedCustAndsignedCustomersBasedOnPrimaryIdReq,
                    createdCustomerWaiverResp: createCustomerWaiverReq,
                }).subscribe({
                    next: (response: any) => {
                        resolve({
                            relatedCustAndsignedCustomersBasedOnPrimaryIdResp:
                                response.relatedCustAndsignedCustomersBasedOnPrimaryIdResp,
                            createdCustomerWaiverResp:
                                response.createdCustomerWaiverResp.data,
                        });
                        console.log(JSON.stringify(response));
                    },
                    error: (err) => {
                        console.error(err);
                    },
                });
            } catch (error) {
                reject(error);
                throw error;
            }
        });
    }

    constructHTMLWaiversForDisplay(createdCustomerWaiverFromAPI: any): any {
        if (
            createdCustomerWaiverFromAPI.CreateCustomerWaiverDTOList.length > 0
        ) {
            createdCustomerWaiverFromAPI.CreateCustomerWaiverDTOList.forEach(
                (waiverItem: any) => {
                    if (waiverItem.CustomerWaiverHTMLForm) {
                        let base64String = waiverItem.CustomerWaiverHTMLForm;
                        // waiverItem.CustomerWaiverHTMLForm = this.previewWaiverService.base64ToUtf8(base64String);
                        // waiverItem.CustomerWaiverHTMLForm = this._sanitizer.bypassSecurityTrustHtml(waiverItem.CustomerWaiverHTMLForm);
                        waiverItem.WaiverType = 'HTML';
                    } else if (waiverItem.WaiverPdfBase64) {
                        waiverItem.WaiverType = 'PDF';
                        waiverItem.WaiverPdf =
                            this._sanitizer.bypassSecurityTrustResourceUrl(
                                `data:application/pdf;base64,${waiverItem.WaiverPdfBase64}`
                            );
                    }
                }
            );
            return createdCustomerWaiverFromAPI;
        } else {
            return null;
        }
    }
    /** Sign wiaver component related functionalities ends  */
}
