import { appCoreConstant } from 'lib-app-core';

export const waiverConstants = {
    ...appCoreConstant,
    API_ENDPOINTS: {
        ...appCoreConstant.API_ENDPOINTS,
        GET_WAIVER_SET_CONTAINER: '/Customer/Waiver/WaiverSetContainer',
        VALIDATE_HTML_WAIVER: '/Product/Waivers/VaidateHTMLWaiver/Customers',
        CREATE_CUSTOMER_WAIVER:
            '/Customer/{custId}/Waiver/{selectedWaiverSetId}/CreateCustomerWaiver?previewMode=true&postMode=false',
        USER_LOGIN: '/Customer/CustomerLogin',
        FORGOT_PASSWORD: '/Customer/PasswordReset',
        VALIDATE_TOKEN: '/Customer/Security/SecurityTokens',
        USER_DETAILS: '/Customer/Customers?customerGUID={CustomerId}',
        PASSWORD_RESET: '/Customer/Customers',
        GET_SIGNED_WAIVERS: '/Customer/Waiver/SignedWaivers'
    },
    SAMPLE_WAIVER: 'Test Waiver',
    TRANSFER_STATE_KEYS: {
        AUTHENTICATE_SYSTEM_USERS_DATA: 'authenticate_system_users_data',
    },
};

/**
 * ================================================
 * USAGE
 * ================================================
 * use this utility function to get constants
 *
 * Ex:
 * getConstantValue();
 * getConstantValue('SAMPLE');
 * getConstantValue('API_ENDPOINTS.AUTHENTICATE_SYSTEM_USERS');
 * ================================================
 */
