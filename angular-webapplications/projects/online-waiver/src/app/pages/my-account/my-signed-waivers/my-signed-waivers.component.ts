import { CommonModule } from '@angular/common';
import { Component, inject, input, OnDestroy, OnInit, signal } from '@angular/core';
import { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';
import { SignedWaiverComponent } from '../../../components/signed-waiver/signed-waiver.component';
import { SignWaiverService } from '../../../core/sign-waiver.service';
import { Subscription } from 'rxjs';
import { CustomerSignedWaiverDTO } from '../../../models/customer-signed-waiver.model';


@Component({
    selector: 'app-my-signed-waivers',
    imports: [CommonModule,SignedWaiverComponent],
    templateUrl: './my-signed-waivers.component.html',
    styleUrl: './my-signed-waivers.component.scss'
})
export class MySignedWaiversComponent implements OnInit,OnDestroy {
    private signedWaiverService = inject(SignWaiverService);
    private subscription?: Subscription;

    signedWaivers = signal<CustomerSignedWaiverDTO[]>([]);
    isLoading = signal<boolean>(false);
    errorMessage = signal<string | null>(null);


    ngOnInit(): void {
        this.loadSignedWaivers();
    }

    ngOnDestroy(): void {
        if (this.subscription) {
            this.subscription.unsubscribe();
        }
    }

    private loadSignedWaivers() {
        const customerId=226;

        if (!customerId) {
            this.errorMessage.set('Customer ID not found. Please log in again.');
            return;
        }

        this.isLoading.set(true);
        this.errorMessage.set(null);

        this.subscription = this.signedWaiverService.getSignedWaivers(customerId).subscribe({
            next:(response) => {
                this.signedWaivers.set(response.data || []);
                this.isLoading.set(false);
            },
            error: (error) => {
                console.log(error)
                this.errorMessage.set('Failed to load signed waivers.');
                this.isLoading.set(false);
            }
        })

    }

    
}
