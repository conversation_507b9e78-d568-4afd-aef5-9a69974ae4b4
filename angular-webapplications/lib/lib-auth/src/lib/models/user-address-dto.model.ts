/**
 * @fileoverview Model for user address
 * <AUTHOR>
 * @version 1.0.0
 */
import { BaseDTO } from 'lib/lib-app-core/src/lib/models/base-dto.model';

export class UserAddressDTOModel extends BaseDTO<UserAddressDTOModel> {
    Id!: number;
    ProfileId!: number;
    AddressTypeId!: number;
    AddressType!: number;
    Line1!: string | null;
    Line2!: string | null;
    Line3!: string | null;
    City!: string | null;
    StateId!: number;
    CountryId!: number;
    PostalCode!: string | null;
    StateCode!: string | null;
    StateName!: string | null;
    CountryName!: string | null;
    IsActive!: boolean;
    CreatedBy!: string | null;
    CreationDate!: string | null;
    LastUpdatedBy!: string | null;
    LastUpdateDate!: string | null;
    SiteId!: number;
    MasterEntityId!: number;
    SynchStatus!: boolean;
    Guid!: string | null;
    constructor() {
        super();
    }
}
