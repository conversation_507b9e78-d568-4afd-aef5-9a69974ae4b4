/**
 * @fileoverview Data layer service for handling login API calls
 * <AUTHOR>
 * @version 1.0.0
 */
import { Injectable } from '@angular/core';
import { ApiServiceBase } from 'lib-app-core';
import { Observable, tap } from 'rxjs';
import { LibAuthLoginAPIParams } from '../interfaces/login-form.interface';

@Injectable()
export class LibAuthLoginDL extends ApiServiceBase {
    private _apiParams!: LibAuthLoginAPIParams;

    constructor() {
        // Call the parent constructor with the specific API endpoint and action type
        super('user_login_data', 'USER_LOGIN');
        this.init();
    }

    // Builds the API parameters for the login request
    // This method is called by the business layer to set the parameters before making the API call
    buildApiParams(data: LibAuthLoginAPIParams) {
        this._apiParams = data;
    }

    // Returns the API URL for the login request
    // This method is used to construct the URL for the API call
    load(): Observable<any> {
        const url = `${this.getApiUrl()}`;
        const loginPayload = this._apiParams;

        return this._http.post<any>(url, loginPayload)
    }
}
